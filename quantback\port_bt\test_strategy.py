#!/usr/bin/env python3
"""
测试Strategy类的基本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quantback.port_bt.strategy import Strategy
from quantback.port_bt.portfolio import OrderCost


class TestStrategy(Strategy):
    """测试策略"""
    
    def initialize(self):
        """策略初始化"""
        print("TestStrategy.initialize() 被调用")
        
        # 设置基准
        self.set_benchmark('000300.SH')
        
        # 设置交易成本
        self.set_order_cost(
            OrderCost(
                open_tax=0.0,
                close_tax=0.001,
                open_commission=0.0003,
                close_commission=0.0003,
                close_today_commission=0.0,
                min_commission=5.0,
            ),
            type='stock',
        )
        
        # 设置策略参数
        self.g.test_param = "测试参数"
        
        # 注册定时任务
        self.run_daily(self.handle_data, time='9:30')
        
        print(f"基准设置为: {self.benchmark}")
        print(f"测试参数: {self.g.test_param}")
        print(f"初始资金: {self.portfolio.starting_cash}")
        print(f"可用资金: {self.portfolio.available_cash}")
    
    def handle_data(self, strategy):
        """数据处理函数"""
        print(f"handle_data 被调用，当前时间: {self.current_dt}")
        print(f"可用资金: {self.portfolio.available_cash}")


def test_strategy_creation():
    """测试策略创建"""
    print("=" * 50)
    print("测试Strategy类创建")
    print("=" * 50)
    
    # 创建策略实例
    strategy = TestStrategy(initial_cash=1000000)
    
    # 测试基本属性
    print(f"策略类名: {strategy.__class__.__name__}")
    print(f"初始资金: {strategy.portfolio.starting_cash}")
    print(f"可用资金: {strategy.portfolio.available_cash}")
    print(f"当前时间: {strategy.current_dt}")
    print(f"基准: {strategy.benchmark}")
    
    # 执行初始化
    print("\n执行策略初始化...")
    strategy.initialize()
    
    # 检查初始化后的状态
    print(f"\n初始化后的状态:")
    print(f"基准: {strategy.benchmark}")
    print(f"定时任务数量: {len(strategy.get_scheduled_tasks())}")
    print(f"交易成本配置: {strategy.order_costs}")
    
    return strategy


def test_strategy_methods():
    """测试策略方法"""
    print("\n" + "=" * 50)
    print("测试Strategy类方法")
    print("=" * 50)
    
    strategy = TestStrategy(initial_cash=1000000)
    strategy.initialize()
    
    # 测试交易方法（不会真正执行，因为没有市场数据）
    print("\n测试交易方法...")
    try:
        # 这些方法应该能正常调用，但可能因为没有市场数据而失败
        print("测试 order 方法...")
        result = strategy.order('000001.SZ', 100)
        print(f"order 结果: {result}")
    except Exception as e:
        print(f"order 方法异常（预期）: {e}")
    
    try:
        print("测试 order_value 方法...")
        result = strategy.order_value('000001.SZ', 10000)
        print(f"order_value 结果: {result}")
    except Exception as e:
        print(f"order_value 方法异常（预期）: {e}")
    
    # 测试数据获取方法
    print("\n测试数据获取方法...")
    try:
        result = strategy.get_price('000001.SZ', count=5)
        print(f"get_price 结果: {result}")
    except Exception as e:
        print(f"get_price 方法异常（预期）: {e}")


def main():
    """主测试函数"""
    print("开始测试Strategy类...")
    
    try:
        # 测试策略创建
        strategy = test_strategy_creation()
        
        # 测试策略方法
        test_strategy_methods()
        
        print("\n" + "=" * 50)
        print("✅ Strategy类基本功能测试完成")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
