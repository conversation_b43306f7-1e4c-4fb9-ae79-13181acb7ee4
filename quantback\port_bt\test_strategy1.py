#!/usr/bin/env python3
"""
测试ETF轮动策略的基本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quantback.port_bt.strategies.strategy1 import ETFRotationStrategy


def test_etf_strategy():
    """测试ETF轮动策略"""
    print("=" * 50)
    print("测试ETF轮动策略")
    print("=" * 50)
    
    # 创建策略实例
    strategy = ETFRotationStrategy(initial_cash=1000000)
    
    print(f"策略类名: {strategy.__class__.__name__}")
    print(f"初始资金: {strategy.portfolio.starting_cash}")
    
    # 执行初始化
    print("\n执行策略初始化...")
    strategy.initialize()
    
    # 检查初始化后的状态
    print(f"\n初始化后的状态:")
    print(f"基准: {strategy.benchmark}")
    print(f"定时任务数量: {len(strategy.get_scheduled_tasks())}")
    print(f"ETF池: {strategy.g.etf_pool}")
    print(f"动量参考天数: {strategy.g.m_days}")
    print(f"交易成本配置: {strategy.order_costs}")
    
    # 测试方法存在性
    print(f"\n方法检查:")
    print(f"✓ MOM方法存在: {hasattr(strategy, 'MOM')}")
    print(f"✓ get_rank方法存在: {hasattr(strategy, 'get_rank')}")
    print(f"✓ trade方法存在: {hasattr(strategy, 'trade')}")
    print(f"✓ sell方法存在: {hasattr(strategy, 'sell')}")
    
    return strategy


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n" + "=" * 50)
    print("测试向后兼容性")
    print("=" * 50)
    
    # 测试传统函数是否存在
    from quantback.port_bt.strategies.strategy1 import (
        initialize, MOM_legacy, get_rank_legacy, trade_legacy, sell_legacy
    )
    
    print("✓ 传统函数存在:")
    print(f"  - initialize: {initialize}")
    print(f"  - MOM_legacy: {MOM_legacy}")
    print(f"  - get_rank_legacy: {get_rank_legacy}")
    print(f"  - trade_legacy: {trade_legacy}")
    print(f"  - sell_legacy: {sell_legacy}")


def main():
    """主测试函数"""
    print("开始测试ETF轮动策略...")
    
    try:
        # 测试策略创建和初始化
        strategy = test_etf_strategy()
        
        # 测试向后兼容性
        test_backward_compatibility()
        
        print("\n" + "=" * 50)
        print("✅ ETF轮动策略测试完成")
        print("=" * 50)
        print("\n总结:")
        print("1. ✅ ETFRotationStrategy类功能正常")
        print("2. ✅ 策略初始化成功")
        print("3. ✅ 所有必要方法存在")
        print("4. ✅ 向后兼容函数存在")
        print("5. ✅ 参数配置正确")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
