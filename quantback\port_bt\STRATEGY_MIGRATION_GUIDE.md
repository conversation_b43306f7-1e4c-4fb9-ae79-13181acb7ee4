# Strategy类迁移指南

## 概述

QuantBack回测框架已从函数式API重构为面向对象的Strategy类设计，参考了主流回测框架（Backtrader、QuantConnect Lean、vn.py）的架构模式。

## 主要变化

### 1. 从函数式到面向对象

**旧方式（函数式）：**
```python
from quantback.port_bt.api import *
from quantback.port_bt.context import Context, g

def initialize(context: Context):
    set_benchmark('000300.SH')
    set_order_cost(OrderCost(...), type='stock')
    g.universe = ['000001.SZ', '000002.SZ']
    run_daily(handle_data, time='9:30')

def handle_data(context: Context):
    for symbol in g.universe:
        order_value(symbol, 10000)
```

**新方式（面向对象）：**
```python
from quantback.port_bt.strategy import Strategy
from quantback.port_bt.portfolio import OrderCost

class MyStrategy(Strategy):
    def initialize(self):
        self.set_benchmark('000300.SH')
        self.set_order_cost(OrderCost(...), type='stock')
        self.g.universe = ['000001.SZ', '000002.SZ']
        self.run_daily(self.handle_data, time='9:30')
    
    def handle_data(self, strategy):
        for symbol in self.g.universe:
            self.order_value(symbol, 10000)
```

### 2. 上下文访问方式

| 功能 | 旧方式 | 新方式 |
|------|--------|--------|
| 投资组合 | `context.portfolio` | `self.portfolio` |
| 当前时间 | `context.current_dt` | `self.current_dt` |
| 前一交易日 | `context.previous_date` | `self.previous_date` |
| 基准设置 | `set_benchmark()` | `self.set_benchmark()` |
| 全局变量 | `g.param` | `self.g.param` |
| 交易函数 | `order()` | `self.order()` |
| 数据获取 | `get_price()` | `self.get_price()` |

### 3. 回测引擎使用

**旧方式：**
```python
from quantback.port_bt.engine import BacktestEngine
from my_strategy import initialize

engine = BacktestEngine(initialize_func=initialize, strategy_config=config)
results = engine.run()
```

**新方式：**
```python
from quantback.port_bt.engine import BacktestEngine
from my_strategy import MyStrategy

strategy = MyStrategy(initial_cash=1000000)
engine = BacktestEngine(strategy=strategy, strategy_config=config)
results = engine.run()
```

## 迁移步骤

### 步骤1：创建Strategy子类
```python
from quantback.port_bt.strategy import Strategy

class MyStrategy(Strategy):
    def initialize(self):
        # 将原来的initialize函数内容移到这里
        pass
```

### 步骤2：转换API调用
- `set_benchmark()` → `self.set_benchmark()`
- `set_order_cost()` → `self.set_order_cost()`
- `run_daily()` → `self.run_daily()`
- `order()` → `self.order()`
- `get_price()` → `self.get_price()`
- `g.param` → `self.g.param`

### 步骤3：转换定时任务函数
```python
# 旧方式
def handle_data(context: Context):
    # 使用context访问数据
    pass

# 新方式
def handle_data(self, strategy):
    # 使用self访问数据
    pass
```

### 步骤4：更新运行器
```python
# 创建策略实例
strategy = MyStrategy(initial_cash=1000000)

# 创建引擎
engine = BacktestEngine(strategy=strategy, strategy_config=config)

# 运行回测
results = engine.run()
```

## 向后兼容性

框架保持了完全的向后兼容性：

1. **传统API仍然可用**：所有旧的函数式API都保留，但会发出弃用警告
2. **引擎支持两种模式**：BacktestEngine既支持Strategy实例，也支持传统的initialize函数
3. **渐进式迁移**：可以逐步迁移现有策略，不需要一次性全部更改

## 优势

### 1. 策略上下文隔离
- 每个策略实例都有独立的上下文
- 为多策略并发执行奠定基础
- 避免全局状态污染

### 2. 更好的代码组织
- 策略逻辑封装在类中
- 更清晰的方法调用关系
- 便于继承和扩展

### 3. 符合主流框架设计
- 与Backtrader、QuantConnect等框架保持一致
- 降低学习成本
- 便于框架间迁移

## 示例对比

### 完整示例：双均线策略

**旧版本：**
```python
def initialize(context: Context):
    set_benchmark('000300.SH')
    g.short_window = 5
    g.long_window = 20
    g.universe = ['000001.SZ']
    run_daily(handle_data, time='9:30')

def handle_data(context: Context):
    for symbol in g.universe:
        prices_df = get_price(symbol, count=g.long_window, fields=['close'])
        if len(prices_df) < g.long_window:
            continue
        
        short_ma = prices_df['close'].tail(g.short_window).mean()
        long_ma = prices_df['close'].mean()
        
        if short_ma > long_ma:
            order_value(symbol, 10000)
```

**新版本：**
```python
class DualMAStrategy(Strategy):
    def initialize(self):
        self.set_benchmark('000300.SH')
        self.g.short_window = 5
        self.g.long_window = 20
        self.g.universe = ['000001.SZ']
        self.run_daily(self.handle_data, time='9:30')

    def handle_data(self, strategy):
        for symbol in self.g.universe:
            prices_df = self.get_price(symbol, count=self.g.long_window, fields=['close'])
            if len(prices_df) < self.g.long_window:
                continue
            
            short_ma = prices_df['close'].tail(self.g.short_window).mean()
            long_ma = prices_df['close'].mean()
            
            if short_ma > long_ma:
                self.order_value(symbol, 10000)
```

## 注意事项

1. **定时任务函数签名**：新版本中定时任务函数接收`strategy`参数而不是`context`
2. **日志访问**：使用`self.log`而不是全局`log`
3. **初始资金**：在创建Strategy实例时指定，而不是在配置中
4. **抽象方法**：必须实现`initialize()`方法

## 获取帮助

如果在迁移过程中遇到问题，可以：
1. 查看`example_strategy.py`中的完整示例
2. 运行`test_compatibility.py`验证兼容性
3. 查看弃用警告信息获取迁移建议
