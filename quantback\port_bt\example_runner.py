#!/usr/bin/env python3
"""
示例策略运行器
使用BacktestEngine运行双均线策略示例
"""

import logging

from quantback.log_setup import setup_logging
from quantback.market_interface.market_data import profiler
from quantback.port_bt.engine import BacktestEngine
from quantback.port_bt.strategies.example_strategy import ExampleStrategy


def main():
    """运行双均线策略示例"""
    # 定义策略配置
    strategy_config = {
        'start_date': '2024-01-01',
        'end_date': '2024-01-31',
        'initial_cash': 1000000,
    }

    print('=' * 60)
    print('QuantBack 双均线策略回测示例 - 面向对象版本')
    print('=' * 60)

    print('\n策略信息:')
    print(f'  回测期间: {strategy_config["start_date"]} 到 {strategy_config["end_date"]}')
    print(f'  初始资金: {strategy_config["initial_cash"]:,} 元')

    try:
        # 创建策略实例
        print(f'\n正在创建策略实例...')
        strategy = ExampleStrategy(initial_cash=strategy_config['initial_cash'])

        # 创建回测引擎
        print(f'正在创建回测引擎...')
        engine = BacktestEngine(strategy=strategy, strategy_config=strategy_config)

        # 运行回测
        print(f'开始运行回测...')
        results = engine.run()

        # 打印回测结果
        print('\n' + '=' * 60)
        print('回测结果')
        print('=' * 60)

        # 基本收益指标
        stats = results['performance_stats']
        print(f'\n📊 收益指标:')
        print(f'  总收益率:     {stats["total_return"]:>8.2%}')
        print(f'  年化收益率:   {stats["annual_return"]:>8.2%}')
        print(f'  最大回撤:     {stats["max_drawdown"]:>8.2%}')
        print(f'  夏普比率:     {stats["sharpe_ratio"]:>8.2f}')

        # 风险指标
        print(f'\n📈 风险指标:')
        print(f'  年化波动率:   {stats["annual_volatility"]:>8.2%}')
        print(f'  95% VaR:      {stats["var_95"]:>8.2%}')

        # 交易统计
        print(f'\n💼 交易统计:')
        print(f'  总交易次数:   {stats["total_trades"]:>8}')
        print(f'  盈利交易:     {stats["winning_trades"]:>8}')
        print(f'  亏损交易:     {stats["losing_trades"]:>8}')
        total_trades_with_result = stats['winning_trades'] + stats['losing_trades']
        if total_trades_with_result > 0:
            win_rate = stats['winning_trades'] / total_trades_with_result
            print(f'  胜率:         {win_rate:>8.2%}')
        else:
            print(f'  胜率:         {"N/A":>8}')
        print(f'  平均盈利:     {stats["avg_win"]:>8.2%}')
        print(f'  平均亏损:     {stats["avg_loss"]:>8.2%}')

        # 基准比较
        if 'benchmark_total_return' in stats:
            print(f'\n📊 基准比较:')
            print(f'  基准总收益:   {stats["benchmark_total_return"]:>8.2%}')
            print(f'  基准年化:     {stats["benchmark_annual_return"]:>8.2%}')
            print(f'  超额收益:     {stats["excess_return"]:>8.2%}')
            print(f'  信息比率:     {stats["information_ratio"]:>8.2f}')
            print(f'  Beta:         {stats["beta"]:>8.2f}')
            print(f'  Alpha:        {stats["alpha"]:>8.2%}')

        # 投资组合历史
        portfolio_history = results['portfolio_history']
        print(f'\n📈 投资组合:')
        print(f'  期初总资产:   {portfolio_history["total_value"].iloc[0]:>12,.2f} 元')
        print(f'  期末总资产:   {portfolio_history["total_value"].iloc[-1]:>12,.2f} 元')
        print(f'  期末现金:     {portfolio_history["available_cash"].iloc[-1]:>12,.2f} 元')
        print(f'  期末市值:     {portfolio_history["market_value"].iloc[-1]:>12,.2f} 元')

        # 交易记录摘要
        if 'trade_records' in results and results['trade_records']:
            trades = results['trade_records']
            print(f'\n📋 交易记录摘要:')
            print(f'  交易记录数:   {len(trades):>8}')
            print('  (详细交易记录已保存在结果中)')

        print('\n' + '=' * 60)
        print('回测完成！')
        print('=' * 60)

        return results

    except Exception as e:
        print(f'\n❌ 回测运行失败: {e}')
        import traceback

        traceback.print_exc()
        return None


if __name__ == '__main__':
    setup_logging()
    main()
    # 性能优化：打印性能分析结果
    # profiler.print_stats()
