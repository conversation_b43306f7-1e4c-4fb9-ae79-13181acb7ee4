"""
双均线策略示例
"""

from quantback.port_bt.api import *
from quantback.port_bt.context import Context, g


def initialize(context: Context):
    """
    初始化函数

    Args:
        context: 回测上下文
    """
    # 设置基准
    set_benchmark('000300.SH')  # 沪深300

    # 设置交易成本
    set_order_cost(
        OrderCost(
            open_tax=0.0,  # 买入无印花税
            close_tax=0.001,  # 卖出千分之一印花税
            open_commission=0.0003,  # 买入万分之三佣金
            close_commission=0.0003,  # 卖出万分之三佣金
            close_today_commission=0.0,  # 平今仓佣金
            min_commission=5.0,  # 最低5元佣金
        ),
        type='stock',
    )

    # 策略参数
    g.short_window = 5  # 短期均线窗口
    g.long_window = 20  # 长期均线窗口

    # 设置股票池（使用g对象，便于动态调整）
    g.universe = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']

    # 设置定时任务
    run_daily(handle_data, time='9:30')  # 每天9:30执行交易逻辑
    run_daily(market_close, time='15:00')  # 每天15:00执行收盘后处理

    log.info(f'策略初始化完成，股票池: {g.universe}')
    log.info(f'均线参数: 短期{g.short_window}日，长期{g.long_window}日')


def handle_data(context: Context):
    """
    数据处理函数

    Args:
        context: 回测上下文
    """
    # 对每只股票执行交易逻辑
    for symbol in g.universe:
        # 获取历史价格数据用于计算均线
        prices_df = get_price(
            symbol, end_date=context.previous_date, count=g.long_window, fields=['close']
        )
        if prices_df is None or len(prices_df) < g.long_window:
            continue

        # 提取close列的Series进行均线计算
        prices = prices_df['close']

        # 计算均线
        short_ma = prices.tail(g.short_window).mean()
        long_ma = prices.mean()

        # 获取当前持仓
        current_volume = 0
        if symbol in context.portfolio.positions:
            current_volume = context.portfolio.positions[symbol].total_volume

        # 交易逻辑：短期均线上穿长期均线买入，下穿卖出
        if short_ma > long_ma and current_volume == 0:
            # 买入信号 - 使用可用资金的20%，避免资金不足
            available_cash = context.portfolio.available_cash
            target_value = available_cash * 0.2  # 每只股票最多占可用资金的20%

            log.info(
                f'买入信号: {symbol}, 短期均线: {short_ma:.2f}, 长期均线: {long_ma:.2f}, 买入金额: {target_value:.2f}元'
            )
            order_value(symbol, target_value)  # 使用默认的市价单

        elif short_ma < long_ma and current_volume > 0:
            # 卖出信号
            log.info(f'卖出信号: {symbol}, 短期均线: {short_ma:.2f}, 长期均线: {long_ma:.2f}')
            order_target(symbol, 0)  # 使用默认的市价单


def market_close(context: Context):
    """
    收盘后函数

    Args:
        context: 回测上下文
    """
    pass
