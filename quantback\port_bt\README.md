# QuantBack 回测框架 v2.0

## 概述

QuantBack是一个面向对象的股票量化回测框架，采用Strategy类设计模式，参考了主流回测框架（Backtrader、QuantConnect Lean、vn.py）的架构。

## 核心特性

- **面向对象设计**：基于Strategy基类的策略开发模式
- **策略上下文隔离**：每个策略实例都有独立的上下文
- **灵活的时间调度**：支持run_daily机制，可指定具体时间或'every_minute'
- **完整的交易功能**：支持市价单、限价单等多种订单类型
- **丰富的数据接口**：集成ClickHouse数据源
- **详细的统计分析**：提供完整的回测结果分析

## 快速开始

### 1. 创建策略

```python
from quantback.port_bt.strategy import Strategy
from quantback.port_bt.portfolio import OrderCost

class MyStrategy(Strategy):
    def initialize(self):
        # 设置基准
        self.set_benchmark('000300.SH')
        
        # 设置交易成本
        self.set_order_cost(
            OrderCost(
                open_tax=0.0,
                close_tax=0.001,
                open_commission=0.0003,
                close_commission=0.0003,
                close_today_commission=0.0,
                min_commission=5.0,
            ),
            type='stock',
        )
        
        # 策略参数
        self.g.universe = ['000001.SZ', '000002.SZ']
        
        # 注册定时任务
        self.run_daily(self.handle_data, time='9:30')
    
    def handle_data(self, strategy):
        # 策略逻辑
        for symbol in self.g.universe:
            self.order_value(symbol, 10000)
```

### 2. 运行回测

```python
from quantback.port_bt.engine import BacktestEngine

# 创建策略实例
strategy = MyStrategy(initial_cash=1000000)

# 配置回测参数
strategy_config = {
    'start_date': '2024-01-01',
    'end_date': '2024-12-31',
    'initial_cash': 1000000,
}

# 创建并运行回测引擎
engine = BacktestEngine(strategy=strategy, strategy_config=strategy_config)
results = engine.run()

# 查看结果
print(results['performance_stats'])
```

## 核心组件

### Strategy基类

所有策略都应该继承Strategy基类：

- `initialize()`: 策略初始化方法（必须实现）
- `run_daily()`: 注册定时任务
- `order()`, `order_value()`, `order_target()`: 交易方法
- `get_price()`: 获取历史数据
- `portfolio`: 投资组合对象
- `g`: 全局参数对象

### BacktestEngine

回测引擎负责执行策略：

- 支持Strategy实例
- 处理时间调度和事件驱动
- 管理投资组合状态
- 生成回测结果

## 示例策略

### 双均线策略

参见 `strategies/example_strategy.py`

### ETF轮动策略

参见 `strategies/strategy1.py`

## 架构优势

1. **策略隔离**：每个策略实例独立运行，互不干扰
2. **面向对象**：清晰的类结构，便于扩展和维护
3. **类型安全**：完整的类型注解，IDE友好
4. **测试友好**：易于单元测试和集成测试
5. **多策略支持**：为并发执行多策略奠定基础

## 版本历史

- **v2.0**: 完全面向对象的Strategy类设计
- **v1.0**: 函数式API设计（已废弃）

## 注意事项

- v2.0版本不再支持传统的函数式API
- 所有策略都必须继承Strategy基类
- 定时任务函数接收strategy参数而不是context参数
