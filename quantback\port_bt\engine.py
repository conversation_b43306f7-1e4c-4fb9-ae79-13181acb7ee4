"""
主回测引擎，事件驱动架构
"""

import logging
import warnings
from datetime import datetime, time, timedelta
from typing import Any, Callable, Dict, List, Optional, Union

import pandas as pd

from quantback.market_interface.market_data import get_kline_data, get_trading_calendar

from .portfolio import Portfolio
from .statistics import Statistics

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')


# 缓存交易时间分钟列表
_TRADING_MINUTES_CACHE: Optional[List[time]] = None


# 这些全局函数已不再需要，Strategy类内部管理定时任务


class ScheduledTask:
    """定时任务类"""

    def __init__(self, func: Callable[['Strategy'], None], time_spec: Union[str, time]):
        """
        初始化定时任务

        Args:
            func: 要执行的函数
            time_spec: 时间规格，可以是 'every_minute' 或具体时间如 '9:30'
        """
        self.func = func
        self.time_spec = time_spec

        # 解析时间规格
        if time_spec == 'every_minute':
            self.is_every_minute = True
            self.target_time = None
        else:
            self.is_every_minute = False
            if isinstance(time_spec, str):
                # 解析时间字符串，如 '9:30'
                hour, minute = map(int, time_spec.split(':'))
                self.target_time = time(hour, minute)
            else:
                self.target_time = time_spec


class BacktestEngine:
    """回测引擎"""

    def __init__(
        self,
        strategy: Union[Callable[[], 'Strategy'], 'Strategy'],
        strategy_config: Dict[str, Any],
        progress_callback: Optional[
            Callable[[int, int, float, float, Optional[float]], None]
        ] = None,
    ):
        """
        初始化回测引擎

        Args:
            strategy: 策略实例或策略类
            strategy_config: 策略配置字典，包含 initial_cash, start_date, end_date 等
            progress_callback: 进度回调函数，接收参数 (current, total, timestamp, total_value, benchmark_value)
        """
        # 策略配置
        self.strategy_config = strategy_config
        self.initial_cash = strategy_config.get('initial_cash', 1000000.0)
        self.start_date = strategy_config.get('start_date')
        self.end_date = strategy_config.get('end_date')

        # 处理策略参数
        # 延迟导入避免循环导入
        from .strategy import Strategy

        if isinstance(strategy, Strategy):
            # 直接传入策略实例
            self.strategy_instance = strategy
        elif callable(strategy):
            # 策略类的构造函数
            try:
                # 尝试创建实例
                test_instance = strategy()
                if isinstance(test_instance, Strategy):
                    self.strategy_instance = test_instance
                else:
                    raise ValueError('strategy参数必须是Strategy实例或Strategy类')
            except Exception as e:
                raise ValueError(f'无法创建策略实例: {e}')
        else:
            raise ValueError('strategy参数必须是Strategy实例或Strategy类')

        self.scheduled_tasks: List = []  # 定时任务列表

        # 进度回调
        self.progress_callback = progress_callback

        # 回测状态
        self.trade_dates: List[str] = []

        # 结果记录
        self.results = {}

        # 验证必要参数
        if not self.start_date or not self.end_date:
            raise ValueError('strategy_config必须包含start_date和end_date')

    def get_benchmark_data(
        self, symbol: str, start_date: str, end_date: str
    ) -> Optional[pd.Series]:
        """
        获取基准数据

        Args:
            symbol: 基准代码
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'

        Returns:
            Optional[pd.DataFrame]: 基准数据
        """
        try:
            # 使用get_kline_data获取前复权数据
            df = get_kline_data(
                stock_code=symbol,
                start_date=start_date,
                end_date=end_date,
                period='1d',
                adjust_type='pre',  # 前复权
            )

            return df['close']
        except Exception as e:
            logger.error(f'获取基准数据失败: {e}')
            return None

    def set_scheduled_tasks(self, tasks: List):
        """设置定时任务列表"""
        self.scheduled_tasks = tasks

    def _get_trading_minutes(self) -> List[time]:
        """
        time=every_miniute时, 执行的时间列表.
        上午: 9:30-11:30 (121分钟)
        下午: 13:01-14:59 (119分钟)
        注意：因为午休, 11:30视作紧邻13:00, 所以13:00不执行every_miniute任务.
        15:00收盘也没机会下单了, 也不执行every_miniute任务.

        使用缓存机制，每次程序运行只计算一次
        """
        global _TRADING_MINUTES_CACHE

        # 如果缓存存在，直接返回
        if _TRADING_MINUTES_CACHE is not None:
            return _TRADING_MINUTES_CACHE

        # 计算交易时间分钟列表
        trading_minutes = []

        # 上午交易时间 9:30-11:30
        hour = 9
        start_minute, end_minute = 30, 59
        for minute in range(start_minute, end_minute + 1):
            trading_minutes.append(time(hour, minute))

        hour = 10
        start_minute, end_minute = 0, 59
        for minute in range(start_minute, end_minute + 1):
            trading_minutes.append(time(hour, minute))

        hour = 11
        start_minute, end_minute = 0, 30
        for minute in range(start_minute, end_minute + 1):
            trading_minutes.append(time(hour, minute))

        # 下午交易时间 13:01-14:59
        hour = 13
        start_minute, end_minute = 1, 59
        for minute in range(start_minute, end_minute + 1):
            trading_minutes.append(time(hour, minute))

        hour = 14
        start_minute, end_minute = 0, 59
        for minute in range(start_minute, end_minute + 1):
            trading_minutes.append(time(hour, minute))

        # 缓存结果
        _TRADING_MINUTES_CACHE = trading_minutes

        return trading_minutes

    def _build_execution_schedule(self) -> Dict[time, List]:
        """
        构建执行时间到任务的映射

        Returns:
            Dict[time, List]: 时间点 -> 任务列表的字典
        """
        from collections import defaultdict

        # 使用defaultdict收集任务
        time_to_tasks = defaultdict(list)

        for task in self.scheduled_tasks:
            if task.is_every_minute:
                # 为每个交易分钟添加任务
                for minute_time in self._get_trading_minutes():
                    time_to_tasks[minute_time].append(task)
            else:
                # 添加指定时间的任务
                time_to_tasks[task.target_time].append(task)

        # 直接返回defaultdict，排序在使用时进行
        return time_to_tasks

    def _process_dividends(self, base_date: datetime):
        """
        处理分红送转（在每日定时任务执行前调用）

        Args:
            base_date: 基准日期
        """
        # 获取投资组合
        portfolio = self.strategy_instance.portfolio

        if not portfolio.positions:
            return  # 没有持仓时无需处理分红

        # 获取当前持仓的股票代码
        stock_codes = list(portfolio.positions.keys())
        ex_date_str = base_date.strftime('%Y-%m-%d')

        # 查询分红送转数据
        from quantback.market_interface.market_data import get_dividend_info

        dividend_data = get_dividend_info(stock_codes, ex_date_str)

        if dividend_data is None or dividend_data.empty:
            return  # 当日无分红送转

        logger.info(f'⭐️处理 {ex_date_str} 的分红送转，涉及 {len(dividend_data)} 只股票')

        for _, row in dividend_data.iterrows():
            stock_code = row['stock_code']
            interest = row['interest']  # 每股股利（税前，元）
            stock_bonus = row['stock_bonus']  # 每股红股（股）
            stock_gift = row['stock_gift']  # 每股转增股本（股）
            allot_num = row['allot_num']  # 每股配股数（股）
            allot_price = row['allot_price']  # 配股价格（元）

            if stock_code not in portfolio.positions:
                continue  # 该股票已无持仓

            position = portfolio.positions[stock_code]
            current_shares = position.total_volume

            logger.info(f'  处理 {stock_code} 分红送转：持仓 {current_shares:.0f} 股')

            # 1. 处理现金分红
            if interest > 0:
                dividend_cash = current_shares * interest
                portfolio.available_cash += dividend_cash
                logger.info(
                    f'    现金分红：{interest:.3f}元/股 × {current_shares:.0f}股 = {dividend_cash:.2f}元'
                )

            # 2. 处理送股（红股）
            if stock_bonus > 0:
                bonus_shares = round(current_shares * stock_bonus)  # 四舍五入取整
                position.total_volume += bonus_shares
                position.closeable_volume += bonus_shares
                # 调整平均成本：总成本不变，股数增加
                if position.total_volume > 0:
                    position.avg_cost = position.avg_cost * current_shares / position.total_volume
                logger.info(
                    f'    送股：{stock_bonus:.3f}股/股 × {current_shares:.0f}股 = {bonus_shares:.0f}股'
                )

            # 3. 处理转增股本
            if stock_gift > 0:
                gift_shares = round(current_shares * stock_gift)  # 四舍五入取整
                position.total_volume += gift_shares
                position.closeable_volume += gift_shares
                # 调整平均成本：总成本不变，股数增加
                if position.total_volume > 0:
                    position.avg_cost = position.avg_cost * current_shares / position.total_volume
                logger.info(
                    f'    转增：{stock_gift:.3f}股/股 × {current_shares:.0f}股 = {gift_shares:.0f}股'
                )

            # 4. 处理配股（暂时不参与，承担股权稀释损失）
            if allot_num > 0:
                # TODO: 配股处理 - 由于账户资金可能不足，且配股现象较少，暂时不处理
                # 不参与配股意味着承担股权稀释带来的市值损失
                logger.info(
                    f'    配股：{allot_num:.3f}股/股，价格 {allot_price:.2f}元/股 - 暂不参与（TODO）'
                )

    def _execute_scheduled_tasks(self, base_date: datetime):
        """
        执行每日定时任务

        Args:
            base_date: 基准日期
        """
        # 在执行每日定时任务前，先处理处于除权日的股票的分红送转
        self._process_dividends(base_date)

        if not self.scheduled_tasks:
            return

        # 构建时间点到任务的映射
        time_to_tasks = self._build_execution_schedule()

        # 按时间顺序执行任务
        sorted_times = sorted(time_to_tasks.keys())
        for i, exec_time in enumerate(iterable=sorted_times):
            # 设置当前时间
            current_dt = datetime.combine(base_date.date(), exec_time)

            # 设置当前时间
            self.strategy_instance.current_dt = current_dt
            portfolio = self.strategy_instance.portfolio

            # 在执行每日定时任务前，处理限价单（除了第一个时间点）
            if i > 0:
                prev_time = sorted_times[i - 1]
                prev_dt = datetime.combine(base_date.date(), prev_time)
                portfolio.execute_pending_limit_orders(prev_dt, current_dt)

            # 执行该时间点的所有任务
            tasks_to_execute = time_to_tasks[exec_time]
            for task in tasks_to_execute:
                try:
                    # Strategy类的方法，传入strategy实例
                    task.func(self.strategy_instance)
                except Exception as e:
                    logger.exception(f'执行定时任务时出错 ({exec_time}): {e}')

        # 处理最后一个时间点之后的限价单（到收盘）
        if sorted_times:
            last_time = sorted_times[-1]
            last_dt = datetime.combine(base_date.date(), last_time)
            market_close_dt = datetime.combine(base_date.date(), time(15, 0))
            if last_dt < market_close_dt:
                portfolio = self.strategy_instance.portfolio
                portfolio.execute_pending_limit_orders(last_dt, market_close_dt)

    def _get_minute_market_data(self, symbols: List[str], current_dt: datetime) -> Dict[str, Dict]:
        """
        获取指定时间的1分钟行情数据

        Args:
            symbols: 股票代码列表
            current_dt: 当前时间

        Returns:
            Dict[str, Dict]: 股票代码 -> 行情数据字典
        """
        market_data = {}

        for symbol in symbols:
            try:
                # 获取当前分钟的1分钟行情数据
                from quantback.market_interface.market_data import get_kline_data

                # 格式化时间为查询需要的格式
                date_str = current_dt.strftime('%Y-%m-%d')
                start_datetime = f'{date_str} 09:30:00'
                end_datetime = f'{date_str} 15:00:00'

                # 获取当天的1分钟数据
                df = get_kline_data(
                    stock_code=symbol,
                    start_date=start_datetime,
                    end_date=end_datetime,
                    period='1m',
                    adjust_type='none',  # 不复权，使用原始价格
                    fields=['open', 'high', 'low', 'close', 'high_limit', 'low_limit'],
                )

                if df is not None and len(df) > 0:
                    # 查找当前时间的数据
                    target_time = current_dt.replace(second=0, microsecond=0)

                    # 特殊处理9:30时刻：使用9:31的open价格
                    if target_time.hour == 9 and target_time.minute == 30:
                        # 查找9:31的数据
                        next_minute = target_time.replace(minute=31)
                        matching_rows = df[df.index == next_minute]

                        if len(matching_rows) > 0:
                            row = matching_rows.iloc[0]
                            # 使用9:31的open价格作为9:30的价格
                            market_data[symbol] = {
                                'open': row['open'],
                                'high': row['open'],  # 9:30时刻只有一个价格
                                'low': row['open'],
                                'close': row['open'],
                                'high_limit': row.get('high_limit', None),
                                'low_limit': row.get('low_limit', None),
                            }
                            logger.debug(
                                f'  ✓ 获取到 {symbol} 在 9:30 的数据: {row["open"]:.2f} (使用9:31开盘价)'
                            )
                        else:
                            logger.warning(f'  ✗ 未找到 {symbol} 在 9:31 的数据，无法获取9:30价格')
                    else:
                        # 其他时间正常查找
                        matching_rows = df[df.index == target_time]

                        if len(matching_rows) > 0:
                            row = matching_rows.iloc[0]
                            market_data[symbol] = {
                                'open': row['open'],
                                'high': row['high'],
                                'low': row['low'],
                                'close': row['close'],
                                'high_limit': row.get('high_limit', None),
                                'low_limit': row.get('low_limit', None),
                            }
                            logger.debug(
                                f'  ✓ 获取到 {symbol} 在 {target_time.strftime("%H:%M")} 的数据: {row["close"]:.2f}'
                            )
                        else:
                            logger.warning(
                                f'  ✗ 未找到 {symbol} 在 {target_time.strftime("%H:%M")} 的数据'
                            )

            except Exception as e:
                logger.error(f'获取 {symbol} 分钟行情数据失败: {e}')

        return market_data

    def _record_daily_portfolio_value(self, base_dt: datetime):
        """
        记录每日投资组合价值到历史数据

        Args:
            base_dt: 基准日期
        """
        # 获取投资组合
        portfolio = self.strategy_instance.portfolio

        # 获取收盘价格
        current_prices = {}
        if portfolio.positions:
            # 收集需要价格的股票代码
            symbols = list(portfolio.positions.keys())

            for symbol in symbols:
                try:
                    # 获取当日收盘价, 对停牌股票也适用
                    price_df = get_price(symbol, end_date=base_dt, count=1, fields=['close'])
                    if price_df is not None and not price_df.empty:
                        current_prices[symbol] = price_df['close'].iloc[-1]
                except Exception as e:
                    logger.error(f'获取 {symbol} 收盘价失败: {e}')

        # 记录历史数据（无论是否有持仓都要记录）
        portfolio.record_daily_value(base_dt, current_prices)

    def _print_daily_portfolio_summary(self, base_dt: datetime):
        """
        打印每日投资组合摘要（持仓和资金信息）
        打印内容特意使用英语, 以使表格容易对齐
        Args:
            base_dt: 基准日期
        """
        portfolio = self.context.portfolio

        # 计算总收益率
        total_return = (portfolio.total_value - portfolio.starting_cash) / portfolio.starting_cash

        # 打印日期和总体信息
        print('\n' + '=' * 80)
        print(f'📅 {base_dt.strftime("%Y-%m-%d")} 收盘后投资组合摘要')
        print('=' * 80)

        # Cash information table
        print('\n💰 Cash Status:')
        print(f'┌{"─" * 23}┬{"─" * 18}┐')
        print(f'│{"Item":<23}│{"Amount (CNY)":<18}│')
        print(f'├{"─" * 23}┼{"─" * 18}┤')
        print(f'│{"Total Assets":<23}│{portfolio.total_value:>18,.2f}│')
        print(f'│{"Available Cash":<23}│{portfolio.available_cash:>18,.2f}│')
        print(f'│{"Locked Cash":<23}│{portfolio.locked_cash:>18,.2f}│')
        print(f'│{"Market Value":<23}│{portfolio.market_value:>18,.2f}│')
        print(f'│{"Total Return":<23}│{total_return:>18.2%}│')
        print(f'└{"─" * 23}┴{"─" * 18}┘')

        # Position information table
        if portfolio.positions:
            print('\n📈 Position Details:')
            print(f'┌{"─" * 10}┬{"─" * 10}┬{"─" * 10}┬{"─" * 12}┬{"─" * 12}┬{"─" * 12}┬{"─" * 10}┐')
            print(
                f'│{"Symbol":<10}│{"Total":<10}│{"Sellable":<10}│{"Cost":<12}│{"Price":<12}│{"Value":<12}│{"P&L%":<10}│'
            )
            print(f'├{"─" * 10}┼{"─" * 10}┼{"─" * 10}┼{"─" * 12}┼{"─" * 12}┼{"─" * 12}┼{"─" * 10}┤')

            for symbol, position in portfolio.positions.items():
                if position.total_volume > 0:  # Only show positions with holdings
                    pnl_pct = (
                        ((position.price - position.avg_cost) / position.avg_cost)
                        if position.avg_cost > 0
                        else 0
                    )
                    pnl_color = '🟢' if pnl_pct > 0 else '🔴' if pnl_pct < 0 else '⚪'

                    print(
                        f'│{symbol:<10}│{position.total_volume:>10}│{position.closeable_volume:>10}│'
                        f'{position.avg_cost:>12.2f}│{position.price:>12.2f}│{position.value:>12,.2f}│'
                        f'{pnl_color}{pnl_pct:>8.2%}│'
                    )

            print(f'└{"─" * 10}┴{"─" * 10}┴{"─" * 10}┴{"─" * 12}┴{"─" * 12}┴{"─" * 12}┴{"─" * 10}┘')
        else:
            print('\n📈 Position Details: No positions')

    def _daily_market_close_cleanup(self):
        """每日收盘时的清理工作"""
        # 获取投资组合
        portfolio = self.strategy_instance.portfolio

        # 无需取消未成交订单
        # 直接清空所有订单记录，避免干扰下一个交易日
        portfolio.order_manager.clear_all_orders()

        # 解锁冻结的持仓和资金，同时解锁T+1持仓
        portfolio.unlock_all()

    def run(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        initial_cash: Optional[float] = None,
    ) -> Dict[str, Any]:
        """
        运行回测

        Args:
            start_date: 开始日期 'YYYY-MM-DD'，如果不提供则使用策略配置中的值
            end_date: 结束日期 'YYYY-MM-DD'，如果不提供则使用策略配置中的值
            initial_cash: 初始资金，如果不提供则使用策略配置中的值

        Returns:
            Dict[str, Any]: 回测结果
        """
        # 使用传入参数或策略配置中的默认值
        actual_start_date = start_date or self.start_date
        actual_end_date = end_date or self.end_date
        actual_initial_cash = initial_cash or self.initial_cash

        logger.info(f'开始回测: {actual_start_date} 到 {actual_end_date}')

        # 获取交易日历（包含首日的上一交易日）
        all_dates = get_trading_calendar(actual_start_date, actual_end_date, include_prev_day=True)
        if not all_dates:
            raise ValueError('无法获取交易日历')

        # 分离前一交易日和实际回测交易日
        self.previous_trading_date = all_dates[0]  # 回测开始前的最后一个交易日
        self.trade_dates = all_dates[1:]  # 实际回测期间的交易日

        logger.info(f'回测前一交易日: {self.previous_trading_date}')
        logger.info(f'回测交易日数量: {len(self.trade_dates)}')

        # 获取基准数据
        benchmark_data = None

        # 策略初始化
        logger.info('初始化策略...')

        # 确保策略实例使用正确的初始资金
        if self.strategy_instance.portfolio.starting_cash != actual_initial_cash:
            # 重新创建Portfolio以使用正确的初始资金
            from .portfolio import Portfolio

            self.strategy_instance.portfolio = Portfolio(
                actual_initial_cash, context=self.strategy_instance
            )

        # 执行策略初始化
        self.strategy_instance.initialize()

        # 将交易成本配置传递给Portfolio
        if hasattr(self.strategy_instance, 'order_costs') and self.strategy_instance.order_costs:
            for cost_type, cost_config in self.strategy_instance.order_costs.items():
                self.strategy_instance.portfolio.set_order_cost(cost_config, cost_type)
            logger.info('使用用户设置的交易成本配置')
        else:
            # 使用默认的股票交易成本
            from .portfolio import DEFAULT_STOCK_COST

            self.strategy_instance.portfolio.set_order_cost(DEFAULT_STOCK_COST, 'stock')
            logger.info('使用默认股票交易成本配置')

        # 获取策略中注册的定时任务
        task_infos = self.strategy_instance.get_scheduled_tasks()
        # 将任务元组转换为ScheduledTask对象
        self.scheduled_tasks = [ScheduledTask(func, time_spec) for func, time_spec in task_infos]
        logger.info(f'获取到 {len(self.scheduled_tasks)} 个定时任务')

        # 获取基准数据（如果在 initialize 中设置了 benchmark）
        if self.strategy_instance.benchmark:
            benchmark_data: pd.Series = self.get_benchmark_data(
                self.strategy_instance.benchmark, self.previous_trading_date, actual_end_date
            )
            logger.info(f'获取基准数据: {self.strategy_instance.benchmark}')
        else:
            logger.info('未设置基准指数，将不进行基准比较')

        # 记录初始投资组合状态（在回测开始前的最后一个交易日）
        # 这样可以为收益率计算提供正确的起点，此时只有初始现金，没有持仓
        if self.trade_dates:
            # 使用回测开始前的最后一个交易日记录初始状态
            initial_date = datetime.strptime(self.previous_trading_date, '%Y-%m-%d')
            self._record_daily_portfolio_value(initial_date)

        # 主循环 - 按交易日遍历
        logger.info('开始主循环...')
        for i, trade_date in enumerate(self.trade_dates):
            # 转换日期格式
            base_dt = datetime.strptime(trade_date, '%Y-%m-%d')
            # 设置前一交易日
            if i > 0:
                # 使用前一个回测交易日
                prev_date = datetime.strptime(self.trade_dates[i - 1], '%Y-%m-%d')
            else:
                # 第一个回测日，使用回测开始前的交易日
                prev_date = datetime.strptime(self.previous_trading_date, '%Y-%m-%d')

            # 设置前一交易日
            self.strategy_instance.previous_date = prev_date

            logger.info(f'处理进度: {i}/{len(self.trade_dates)} ({trade_date})')
            # 调用进度回调
            if self.progress_callback:
                try:
                    benchmark_data_item = (
                        benchmark_data.iloc[i] if benchmark_data is not None else None
                    )
                    # 获取投资组合历史
                    portfolio = self.strategy_instance.portfolio
                    total_value_history = portfolio.history['total_value']
                    total_value_item = total_value_history[-1] if total_value_history else 0
                    timestamp = prev_date.timestamp() * 1000
                    self.progress_callback(
                        i,
                        len(self.trade_dates),
                        timestamp,
                        total_value_item,
                        benchmark_data_item,
                    )
                except Exception as e:
                    logger.warning(f'进度回调执行失败: {e}')

            # 执行定时任务
            self._execute_scheduled_tasks(base_dt)

            # 每日收盘时的清理工作
            self._daily_market_close_cleanup()

            # 每日收盘时记录投资组合价值历史
            self._record_daily_portfolio_value(base_dt)

            # 收盘后打印每日持仓和可用资金, debug时使用
            # self._print_daily_portfolio_summary(base_dt)

        logger.info('回测完成，正在生成结果...')

        # 生成回测结果
        self.results = self._generate_results(benchmark_data)

        return self.results

    def _generate_results(self, benchmark_data: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        生成回测结果

        Args:
            benchmark_data: 基准数据

        Returns:
            Dict[str, Any]: 回测结果
        """
        # 获取投资组合
        portfolio = self.strategy_instance.portfolio

        total_value_data = pd.Series(
            portfolio.history['total_value'],
            index=portfolio.history['timestamp'],
        )

        if total_value_data.empty:
            return {'error': '没有回测数据'}

        # 计算收益率用于统计分析
        returns = total_value_data.pct_change()

        # 基准收益率
        benchmark_returns = None
        if benchmark_data is not None and not benchmark_data.empty:
            # 检查基准数据的索引是否与组合历史的索引完全相同
            portfolio_dates = set(total_value_data.index)
            benchmark_dates = set(benchmark_data.index)

            if portfolio_dates != benchmark_dates:
                missing_in_benchmark = portfolio_dates - benchmark_dates
                extra_in_benchmark = benchmark_dates - portfolio_dates

                if missing_in_benchmark:
                    missing_sorted = sorted(missing_in_benchmark)
                    logger.warning(f'基准数据缺失以下日期: {missing_sorted}')

                if extra_in_benchmark:
                    extra_sorted = sorted(extra_in_benchmark)
                    logger.warning(f'基准数据多出以下日期: {extra_sorted}')

                logger.warning('基准数据索引与组合历史索引不匹配，跳过基准计算')
            else:
                # 索引完全匹配，计算基准收益率
                benchmark_returns = benchmark_data.pct_change()
                # 记录基准价格到投资组合历史数据中
                portfolio.history['benchmark_data'] = benchmark_data.tolist()

        # 计算统计指标
        stats = Statistics()

        performance_stats = stats.calculate_performance(
            returns.dropna(),
            benchmark_returns.dropna() if benchmark_returns is not None else None,
        )

        results = {
            'portfolio_history': portfolio.history,
            'performance_stats': performance_stats,
            'trade_records': portfolio.get_trade_records(),
        }

        return results
