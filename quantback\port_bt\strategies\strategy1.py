"""etf轮动策略"""

import math

import numpy as np
import pandas as pd

from quantback.port_bt.api import *  # noqa: F401, F403
from quantback.port_bt.context import Context, g


# 初始化函数
def initialize(context: Context):
    # 设定基准
    set_benchmark('000300.SH')
    # 设置交易成本
    set_order_cost(
        OrderCost(
            open_tax=0,
            close_tax=0,
            open_commission=0.0002,
            close_commission=0.0002,
            close_today_commission=0,
            min_commission=5,
        ),
        type='stock',
    )

    # 参数
    g.etf_pool = [
        '518880.SH',  # 黄金ETF（大宗商品）
        '513100.SH',  # 纳指100（海外资产）
        '159915.SZ',  # 创业板100（成长股，科技股，中小盘）
        '510180.SH',  # 上证180（价值股，蓝筹股，中大盘）
        '159981.SZ',  # 能源化工ETF（验证能够比有色更好的反应商品走势）
    ]
    g.m_days = 26  # 动量参考天数
    run_daily(trade, '10:56')  # 每天运行确保即时捕捉动量变化
    run_daily(sell, '11:01')  # 止盈止损


def MOM(df):
    y = np.log(df['close'].values)
    n = len(y)
    x = np.arange(n)
    weights = np.linspace(1, 2, n)  # 线性增加权重
    slope, intercept = np.polyfit(x, y, 1, w=weights)
    annualized_returns = math.pow(math.exp(slope), 250) - 1
    residuals = y - (slope * x + intercept)
    weighted_residuals = weights * residuals**2
    r_squared = 1 - (np.sum(weighted_residuals) / np.sum(weights * (y - np.mean(y)) ** 2))
    score = annualized_returns * r_squared
    return score


def get_rank(context: Context):
    score_list = []
    for etf in g.etf_pool:
        df = get_price(
            etf, end_date=context.previous_date, count=g.m_days, period='1d', fields=['close']
        )
        score = MOM(df)
        score_list.append(score)
    df = pd.DataFrame(index=g.etf_pool, data={'score': score_list})
    df = df.sort_values(by='score', ascending=False)
    print(df)

    df = df[(df['score'] > -1) & (df['score'] <= 6)]  # 安全区间，动量过高过低都不好，原先是【0,5】
    rank_list = list(df.index)
    if len(rank_list) == 0:
        rank_list = []  # 如果全部都小于0，那么空仓或者买《国债、银华日历、黄金》避险

    return rank_list


# 交易
def trade(context: Context):
    # 获取动量最高的一只ETF
    target_num = 1
    target_list = get_rank(context)[:target_num]
    # 卖出
    hold_list = list(context.portfolio.positions)
    for etf in hold_list:
        if etf not in target_list:
            order_target_value(etf, 0)
            print('卖出' + str(etf))
        else:
            print('继续持有' + str(etf))
            pass
    # 买入
    hold_list = list(context.portfolio.positions)
    if len(hold_list) < target_num:
        value = context.portfolio.available_cash / (target_num - len(hold_list))
        for etf in target_list:
            if etf not in context.portfolio.positions:
                order_target_value(etf, value)
                print('买入' + str(etf))


# 止盈止损
def sell(context: Context):
    for etf, position in context.portfolio.positions.items():
        if position.closeable_volume == 0:
            continue
        current_data = get_current_market_data(etf)
        # 日内上涨超过n个点，减仓一半
        last = get_price(
            etf, end_date=context.previous_date, count=2, period='1d', fields=['close']
        )
        series = last['close']
        hrate = max(series[1] / series[0], current_data['close'] / series[1]) - 1
        if hrate > 0.05:
            order = order_target_value(etf, position.value / 3)
            if order is not None:
                print(f'日内上涨{hrate * 100:.2f}%，减仓一半!')
